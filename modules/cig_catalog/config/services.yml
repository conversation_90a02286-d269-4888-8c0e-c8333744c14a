services:
  # Repository Services
  cig_catalog.repository.catalog:
    class: CigCatalog\Repository\CatalogRepository
    arguments:
      - '@doctrine.dbal.default_connection'
      - '@prestashop.core.localization.locale.context_locale'
    public: true

  cig_catalog.repository.catalog_order:
    class: CigCatalog\Repository\CatalogOrderRepository
    arguments:
      - '@doctrine.dbal.default_connection'
    public: true

  cig_catalog.repository.catalog_config:
    class: CigCatalog\Repository\CatalogConfigRepository
    arguments:
      - '@doctrine.dbal.default_connection'
    public: true

  # Service Layer
  cig_catalog.service.catalog_manager:
    class: CigCatalog\Service\CatalogManager
    arguments:
      - '@cig_catalog.repository.catalog'
      - '@cig_catalog.service.file_upload'
      - '@cig_catalog.service.cache'
    public: true

  cig_catalog.service.file_upload:
    class: CigCatalog\Service\FileUploadService
    arguments:
      - '%kernel.project_dir%/modules/cig_catalog'
    public: true

  cig_catalog.service.image_optimizer:
    class: CigCatalog\Service\ImageOptimizer
    public: true

  cig_catalog.service.email:
    class: CigCatalog\Service\EmailService
    arguments:
      - '%kernel.project_dir%/modules/cig_catalog'
    public: true

  cig_catalog.service.email_template_manager:
    class: CigCatalog\Service\EmailTemplateManager
    arguments:
      - '%kernel.project_dir%/modules/cig_catalog'
    public: true

  cig_catalog.service.cache:
    class: CigCatalog\Service\CacheService
    arguments:
      - '%kernel.project_dir%/modules/cig_catalog'
      - 'cig_catalog_'
      - 3600
    public: true

  cig_catalog.service.catalog_order:
    class: CigCatalog\Service\CatalogOrderService
    arguments:
      - '@cig_catalog.repository.catalog_order'
      - '@cig_catalog.repository.catalog'
      - '@cig_catalog.service.email'
      - '@cig_catalog.service.cache'
    public: true

  # Validators
  cig_catalog.validator.order:
    class: CigCatalog\Validator\OrderValidator
    arguments:
      - '@translator'
    public: true

  cig_catalog.validator.file_upload:
    class: CigCatalog\Validator\FileUploadValidator
    arguments:
      - '@translator'
    public: true

  # Form Types
  cig_catalog.form.type.catalog:
    class: CigCatalog\Form\Type\CatalogType
    arguments:
      - '@translator'
      - '@prestashop.core.localization.locale.context_locale'
    tags:
      - { name: form.type }

  cig_catalog.form.type.catalog_order:
    class: CigCatalog\Form\Type\CatalogOrderType
    arguments:
      - '@translator'
    tags:
      - { name: form.type }

  cig_catalog.form.type.email_configuration:
    class: CigCatalog\Form\Type\EmailConfigurationType
    arguments:
      - '@translator'
    tags:
      - { name: form.type }

  # Event Listeners
  cig_catalog.listener.catalog_order:
    class: CigCatalog\EventListener\CatalogOrderListener
    arguments:
      - '@cig_catalog.service.email'
      - '@logger'
    tags:
      - { name: kernel.event_listener, event: catalog.order.created, method: onOrderCreated }

  # Security
  cig_catalog.security.csrf_token_manager:
    class: CigCatalog\Security\CsrfTokenManager
    arguments:
      - '@security.csrf.token_manager'
    public: true

  cig_catalog.security.rate_limiter:
    class: CigCatalog\Security\RateLimiter
    arguments:
      - '@cache.app'
      - '@logger'
    public: true

  # Utilities
  cig_catalog.util.slug_generator:
    class: CigCatalog\Util\SlugGenerator
    public: true

  cig_catalog.util.file_helper:
    class: CigCatalog\Util\FileHelper
    arguments:
      - '@logger'
    public: true

  # API Services
  cig_catalog.api.catalog_api:
    class: CigCatalog\Api\CatalogApi
    arguments:
      - '@cig_catalog.service.catalog_manager'
      - '@cig_catalog.service.order_processor'
      - '@cig_catalog.security.csrf_token_manager'
      - '@cig_catalog.security.rate_limiter'
    public: true

  # Statistics and Analytics
  cig_catalog.service.statistics:
    class: CigCatalog\Service\StatisticsService
    arguments:
      - '@cig_catalog.repository.catalog'
      - '@cig_catalog.repository.catalog_order'
      - '@doctrine.dbal.default_connection'
    public: true

  # Configuration Management
  cig_catalog.service.configuration:
    class: CigCatalog\Service\ConfigurationService
    arguments:
      - '@cig_catalog.repository.catalog_config'
      - '@cig_catalog.service.cache_manager'
    public: true

parameters:
  # File Upload Configuration
  cig_catalog.upload.max_file_size: 10485760  # 10MB
  cig_catalog.upload.allowed_image_types: ['jpg', 'jpeg', 'png', 'gif', 'webp']
  cig_catalog.upload.allowed_file_types: ['pdf', 'zip', 'doc', 'docx', 'xls', 'xlsx']
  
  # Image Processing
  cig_catalog.image.thumbnail_size: [300, 200]
  cig_catalog.image.medium_size: [600, 400]
  cig_catalog.image.large_size: [1200, 800]
  cig_catalog.image.quality: 85
  
  # Cache Configuration
  cig_catalog.cache.catalog_list_ttl: 3600      # 1 hour
  cig_catalog.cache.catalog_detail_ttl: 7200    # 2 hours
  cig_catalog.cache.configuration_ttl: 86400    # 24 hours
  
  # Email Configuration
  cig_catalog.email.from_name: 'CIG Catalog System'
  cig_catalog.email.template_path: '@CigCatalog/emails/'
  
  # Security Configuration
  cig_catalog.security.rate_limit_requests: 10
  cig_catalog.security.rate_limit_window: 300   # 5 minutes
  cig_catalog.security.csrf_token_id: 'cig_catalog_token'
  
  # Frontend Configuration
  cig_catalog.frontend.items_per_page: 12
  cig_catalog.frontend.enable_pagination: true
  cig_catalog.frontend.show_download_count: true
  
  # Admin Configuration
  cig_catalog.admin.enable_bulk_operations: true
  cig_catalog.admin.enable_drag_drop: true
  cig_catalog.admin.items_per_page: 20
