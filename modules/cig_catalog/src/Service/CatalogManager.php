<?php

declare(strict_types=1);

namespace CigCatalog\Service;

use CigCatalog\Entity\Catalog;
use CigCatalog\Repository\CatalogRepository;
use CigCatalog\Service\FileUploadService;
use CigCatalog\Service\CacheService;
use PrestaShop\PrestaShop\Core\Domain\Exception\DomainException;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * CatalogManager - hlavní business logika pro správu katalogů
 */
class CatalogManager
{
    private CatalogRepository $catalogRepository;
    private FileUploadService $fileUploadService;
    private CacheService $cacheService;

    public function __construct(
        CatalogRepository $catalogRepository,
        FileUploadService $fileUploadService,
        CacheService $cacheService
    ) {
        $this->catalogRepository = $catalogRepository;
        $this->fileUploadService = $fileUploadService;
        $this->cacheService = $cacheService;
    }

    /**
     * Vytvoří nový katalog
     */
    public function createCatalog(array $data, array $files = []): int
    {
        $this->validateCatalogData($data);
        
        // Kontrola unikátnosti názvu
        if ($this->catalogRepository->existsByTitle($data['title'])) {
            throw new DomainException('Katalog s tímto názvem již existuje');
        }

        $catalog = new Catalog();
        $this->fillCatalogData($catalog, $data);
        
        // Nastavení pozice
        if (!isset($data['position']) || $data['position'] <= 0) {
            $catalog->position = $this->catalogRepository->getNextPosition();
        }

        // Upload souborů
        if (!empty($files['image'])) {
            $catalog->image_path = $this->fileUploadService->uploadImage($files['image']);
        }
        
        if (!empty($files['catalog_file'])) {
            $catalog->catalog_file = $this->fileUploadService->uploadCatalogFile($files['catalog_file']);
        }

        $catalogId = $this->catalogRepository->save($catalog);
        
        // Vyčištění cache
        $this->clearCatalogCache();
        
        return $catalogId;
    }

    /**
     * Aktualizuje existující katalog
     */
    public function updateCatalog(int $id, array $data, array $files = []): bool
    {
        $catalog = $this->catalogRepository->findById($id);
        if (!$catalog) {
            throw new DomainException('Katalog nebyl nalezen');
        }

        $this->validateCatalogData($data, $id);
        
        // Kontrola unikátnosti názvu (kromě aktuálního katalogu)
        if ($this->catalogRepository->existsByTitle($data['title'], $id)) {
            throw new DomainException('Katalog s tímto názvem již existuje');
        }

        $oldImagePath = $catalog->image_path;
        $oldCatalogFile = $catalog->catalog_file;

        $this->fillCatalogData($catalog, $data);

        // Upload nových souborů
        if (!empty($files['image'])) {
            $catalog->image_path = $this->fileUploadService->uploadImage($files['image']);
            // Smazání starého obrázku
            if ($oldImagePath) {
                $this->fileUploadService->deleteFile($oldImagePath);
            }
        }
        
        if (!empty($files['catalog_file'])) {
            $catalog->catalog_file = $this->fileUploadService->uploadCatalogFile($files['catalog_file']);
            // Smazání starého souboru
            if ($oldCatalogFile) {
                $this->fileUploadService->deleteFile($oldCatalogFile);
            }
        }

        $result = $this->catalogRepository->save($catalog);
        
        // Vyčištění cache
        $this->clearCatalogCache();
        
        return $result;
    }

    /**
     * Smaže katalog
     */
    public function deleteCatalog(int $id): bool
    {
        $catalog = $this->catalogRepository->findById($id);
        if (!$catalog) {
            throw new DomainException('Katalog nebyl nalezen');
        }

        // Smazání souborů
        if ($catalog->image_path) {
            $this->fileUploadService->deleteFile($catalog->image_path);
        }
        if ($catalog->catalog_file) {
            $this->fileUploadService->deleteFile($catalog->catalog_file);
        }

        $result = $this->catalogRepository->delete($id);
        
        // Přeuspořádání pozic
        $this->reorderPositionsAfterDelete($catalog->position);
        
        // Vyčištění cache
        $this->clearCatalogCache();
        
        return $result;
    }

    /**
     * Přepne aktivní stav katalogu
     */
    public function toggleActive(int $id): bool
    {
        $catalog = $this->catalogRepository->findById($id);
        if (!$catalog) {
            throw new DomainException('Katalog nebyl nalezen');
        }

        $catalog->active = !$catalog->active;
        $result = $this->catalogRepository->save($catalog);
        
        // Vyčištění cache
        $this->clearCatalogCache();
        
        return $result;
    }

    /**
     * Přeuspořádá katalogy podle nových pozic
     */
    public function reorderCatalogs(array $positions): bool
    {
        foreach ($positions as $id => $position) {
            $catalog = $this->catalogRepository->findById((int)$id);
            if ($catalog) {
                $catalog->position = (int)$position;
                $this->catalogRepository->save($catalog);
            }
        }
        
        // Vyčištění cache
        $this->clearCatalogCache();
        
        return true;
    }

    /**
     * Duplikuje katalog
     */
    public function duplicateCatalog(int $id): int
    {
        $originalCatalog = $this->catalogRepository->findById($id);
        if (!$originalCatalog) {
            throw new DomainException('Katalog nebyl nalezen');
        }

        $newCatalog = clone $originalCatalog;
        $newCatalog->id_catalog = null;
        $newCatalog->title = $originalCatalog->title . ' (kopie)';
        $newCatalog->position = $this->catalogRepository->getNextPosition();
        $newCatalog->active = false; // Kopie je defaultně neaktivní
        
        // Duplikace souborů
        if ($originalCatalog->image_path) {
            $newCatalog->image_path = $this->fileUploadService->duplicateFile($originalCatalog->image_path);
        }
        if ($originalCatalog->catalog_file) {
            $newCatalog->catalog_file = $this->fileUploadService->duplicateFile($originalCatalog->catalog_file);
        }

        $newId = $this->catalogRepository->save($newCatalog);
        
        // Vyčištění cache
        $this->clearCatalogCache();
        
        return $newId;
    }

    /**
     * Hromadné smazání katalogů
     */
    public function bulkDelete(array $ids): bool
    {
        foreach ($ids as $id) {
            $this->deleteCatalog((int)$id);
        }
        return true;
    }

    /**
     * Hromadné přepnutí aktivního stavu
     */
    public function bulkToggleActive(array $ids, bool $active): bool
    {
        foreach ($ids as $id) {
            $catalog = $this->catalogRepository->findById((int)$id);
            if ($catalog) {
                $catalog->active = $active;
                $this->catalogRepository->save($catalog);
            }
        }
        
        // Vyčištění cache
        $this->clearCatalogCache();
        
        return true;
    }

    /**
     * Získá statistiky katalogů
     */
    public function getStatistics(): array
    {
        $cacheKey = 'catalog_statistics';
        $stats = $this->cacheService->get($cacheKey);
        
        if ($stats === null) {
            $stats = [
                'total' => $this->catalogRepository->getTotalCount(),
                'active' => $this->catalogRepository->getActiveCount(),
                'inactive' => $this->catalogRepository->getInactiveCount(),
                'new' => $this->catalogRepository->getNewCount(),
                'with_files' => $this->catalogRepository->getWithFilesCount(),
                'with_images' => $this->catalogRepository->getWithImagesCount(),
            ];
            
            $this->cacheService->set($cacheKey, $stats, 1800); // 30 minut
        }
        
        return $stats;
    }

    /**
     * Validuje data katalogu
     */
    private function validateCatalogData(array $data, ?int $excludeId = null): void
    {
        if (empty($data['title'])) {
            throw new DomainException('Název katalogu je povinný');
        }

        if (strlen($data['title']) > 255) {
            throw new DomainException('Název katalogu je příliš dlouhý (max 255 znaků)');
        }

        if (!empty($data['catalog_url']) && !filter_var($data['catalog_url'], FILTER_VALIDATE_URL)) {
            throw new DomainException('URL katalogu není platná');
        }

        if (isset($data['position']) && (!is_numeric($data['position']) || $data['position'] < 0)) {
            throw new DomainException('Pozice musí být kladné číslo');
        }
    }

    /**
     * Vyplní data do entity katalogu
     */
    private function fillCatalogData(Catalog $catalog, array $data): void
    {
        $catalog->title = $data['title'];
        $catalog->description = $data['description'] ?? '';
        $catalog->catalog_url = $data['catalog_url'] ?? '';
        $catalog->is_new = (bool)($data['is_new'] ?? false);
        $catalog->active = (bool)($data['active'] ?? true);
        
        if (isset($data['position'])) {
            $catalog->position = (int)$data['position'];
        }
    }

    /**
     * Přeuspořádá pozice po smazání katalogu
     */
    private function reorderPositionsAfterDelete(int $deletedPosition): void
    {
        $catalogs = $this->catalogRepository->findByPositionGreaterThan($deletedPosition);
        foreach ($catalogs as $catalog) {
            $catalog->position--;
            $this->catalogRepository->save($catalog);
        }
    }

    /**
     * Vyčistí cache související s katalogy
     */
    private function clearCatalogCache(): void
    {
        $this->cacheService->delete('catalog_statistics');
        $this->cacheService->clear('catalog_list_*');
        $this->cacheService->clear('catalog_detail_*');
    }
}
